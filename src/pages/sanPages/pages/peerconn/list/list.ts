import {Component} from 'san';
import u from 'lodash';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification, Tooltip} from '@baidu/sui';
import {OutlinedRefresh, OutlinedDownload, OutlinedPlus, OutlinedLink, OutlinedEditingSquare} from '@baidu/sui-icon';
import {ResourceGroupDialog} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';

import rules from '../rules';
import WaitingDialog from '../waitingList/list';
import {columns} from './tableFiled';
import Moinitor from './bcmDetail';
import Confirm from '@/pages/sanPages/components/confirm';
import {
    PeerConnStatus,
    PeerConnType,
    PeerConnRole,
    DNSSyncStatus,
    PayType,
    DocService,
    ContextService
} from '@/pages/sanPages/common';
import {utcToTime, urlSerialize, recharge, confirmValidate, $flag as FLAG} from '@/pages/sanPages/utils/helper';
import {isCrossRegion} from '@/pages/sanPages/utils/common';
import {isEnglishLocale} from '@/utils/constants';
import INTRODUCE_ICON from '@/img/introduce_normal.svg?url';
import INTRODUCE_ICON_COLOR from '@/img/introduce_color.svg?url';
import DOCUMENT_ICON from '@/img/document_normal.svg?url';
import DOCUMENT_ICON_COLOR from '@/img/document_color.svg?url';
import alertDialog from './alertDialog';
import CrossAutoRenewDialog from './crossAutoRenewDialog';
import testID from '@/testId';

import './style.less';

const {invokeSUI, invokeSUIBIZ, invokeAppComp, template, invokeComp} = decorators;
const aihcAccountId = '8342d8a8b3654eafba85432324041c47';
const AllRegion = window.$context.getEnum('AllRegion');

const tpl = html`
    <div>
        <s-app-list-page class="peerconn-list-wrap">
            <div class="peerconn-header-wrapper">
                <span class="title">对等连接</span>
                <vpc-select class="vpc-select" on-int="vpcInt" on-change="vpcChange" />
                <div class="list-header-wrap">
                    <div class="header-left">
                        <!--<vpc-select class="vpc-select" on-int="vpcInt" on-change="vpcChange" />-->
                    </div>
                    <div class="header-right">
                        <a
                            s-ref="introduce"
                            href="javascript:void(0)"
                            class="help-file function-introduce"
                            on-click="handleShowCard"
                            on-mouseenter="handleMouseEnter('introduce')"
                            on-mouseleave="handleMouseLeave('introduce')"
                            s-if="{{!FLAG.NetworkSupportXS}}"
                        >
                            <img class="s-icon" src="{{introduceIcon}}" />功能简介
                        </a>
                        <s-button skin="stringfy" class="button-shortcut" on-click="showExpireData" s-if="!showMode">
                            7天即将到期
                        </s-button>
                        <s-button s-else on-click="showExpireData" class="button-shortcut">
                            7天即将到期
                            <s-icon name="close" />
                        </s-button>
                        <div class="link-wrap">
                            <a
                                class="peerconn-tip"
                                s-if="{{!FLAG.NetworkSupportXS}}"
                                target="_BLANK"
                                href="/billing/#/renew/list~serviceType=PEERCONN"
                            >
                                <outlined-link class="outlined-link" color="#2468f2" />自动续费
                            </a>
                            <a
                                class="peerconn-tip"
                                s-if="{{!FLAG.NetworkSupportXS}}"
                                target="_BLANK"
                                href="{{DocService.peer_practice}}"
                            >
                                查看对等连接最佳实践
                            </a>
                        </div>
                        <a
                            href="{{DocService.peer_index}}"
                            target="_blank"
                            class="help-file"
                            on-mouseenter="handleMouseEnter('document')"
                            on-mouseleave="handleMouseLeave('document')"
                            s-if="{{!FLAG.NetworkSupportXS}}"
                        >
                            <img class="s-icon" src="{{documentIcon}}" />帮助文档
                        </a>
                    </div>
                </div>
            </div>
            <div slot="pageTitle">
                <introduce-panel
                    isShow="{{show}}"
                    klass="{{'endpoint-peerconn-wrapper'}}"
                    title="{{introduceTitle}}"
                    description="{{description}}"
                    on-toggle="handleToggle($event)"
                ></introduce-panel>
            </div>
            <div slot="bulk">
                <s-tip-button
                    disabled="{{createPeer.disable || iamPass.disable || accountState.disabled}}"
                    skin="primary"
                    placement="top"
                    isDisabledVisibile="{{true}}"
                    on-click="onCreate"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{createPeer.message|| iamPass.message || accountState.message | raw}}
                    </div>
                    <outlined-plus />
                    创建对等连接
                </s-tip-button>
                <s-tip-button
                    disabled="{{recharge.disable || accountState.disabled}}"
                    isDisabledVisibile="{{true}}"
                    placement="top"
                    on-click="onRecharge"
                    class="left_class"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{recharge.message || accountState.message | raw}}
                    </div>
                    续费
                </s-tip-button>
                <s-tip-button
                    class="left_class"
                    disabled="{{peerconnRelease.disabled}}"
                    on-click="onRelease(table.selectedItems)"
                    isDisabledVisibile="{{true}}"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{peerconnRelease.message | raw}}
                    </div>
                    释放
                </s-tip-button>
                <s-tip-button
                    disabled="{{applyPeer.disable}}"
                    isDisabledVisibile="{{true}}"
                    placement="top"
                    on-click="applyPeer"
                    class="left_class {{isEnglish ? 'hidden' : ''}}"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{applyPeer.message | raw}}
                    </div>
                    连接申请({{applyListNum}})
                </s-tip-button>
                <edit-tag
                    selectedItems="{{table.selectedItems}}"
                    on-success="refresh"
                    type="PEERCONN"
                    class="left_class"
                ></edit-tag>
                <s-tip-button
                    s-if="{{isEnglish}}"
                    disabled="{{applyPeer.disable}}"
                    isDisabledVisibile="{{true}}"
                    placement="top"
                    on-click="applyPeer"
                    class="left_class"
                >
                    <div slot="content">
                        <!--bca-disable-next-line-->
                        {{applyPeer.message | raw}}
                    </div>
                    连接申请({{applyListNum}})
                </s-tip-button>
                <div class="intro-warp" data-intro="这里是批量操作区">
                    <s-tooltip
                        content="请先选择实例对象"
                        trigger="{{operationDisabled ? 'hover' : ''}}"
                        placement="top"
                    >
                        <s-select
                            placeholder="批量操作"
                            value="{=operation=}"
                            disabled="{{operationDisabled}}"
                            class="{{!operationDisabled ? 'placeholder-style' : ''}}"
                            on-change="onOperationChange"
                            data-test-id="${testID.peerconn.listActionSelect}"
                        >
                            <s-select-option
                                class="operation-select"
                                s-for="item, index in OperationType"
                                value="{{item.value}}"
                                label="{{item.label}}"
                                disabled="{{item.disabled}}"
                                data-test-id="${testID.peerconn.listActionOption}{{index}}"
                            >
                                <s-tooltip placement="right" trigger="{{item.message ? 'hover' : ''}}" width="200">
                                    <div slot="content">
                                        <!--bca-disable-next-line-->
                                        {{item.message | raw}}
                                    </div>
                                    <div>{{item.label}}</div>
                                </s-tooltip>
                            </s-select-option>
                        </s-select>
                    </s-tooltip>
                </div>
            </div>
            <div slot="filter">
                <div class="filter-buttons-wrap">
                    <search-tag
                        s-ref="search"
                        serviceType="PEERCONN"
                        searchbox="{=searchbox=}"
                        on-search="onSearch"
                    ></search-tag>
                    <s-button class="button-item s-icon-button" on-click="refresh" track-name="刷新"
                        ><outlined-refresh class="icon-class"
                    /></s-button>
                    <s-button class="button-item download-item s-icon-button" on-click="downLoadList" track-name="下载"
                        ><outlined-download class="icon-class"
                    /></s-button>
                    <custom-column
                        columnList="{{customColumn.datasource}}"
                        initValue="{{customColumn.value}}"
                        type="peerconn"
                        on-init="initColumns"
                        on-change="onCustomColumns"
                    >
                    </custom-column>
                </div>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-selected-change="tableSelected($event)"
                on-filter="onFilter"
                on-sort="onSort"
                selection="{=table.selection=}"
                track-id="ti_vpc_instance_table"
                track-name="列表操作"
                data-test-id="${testID.peerconn.listTable}"
            >
                <div slot="empty">
                    <table-empty
                        dataTestId="${testID.peerconn.listEmpty}"
                        actionAuth="{{iamPass}}"
                        desc="暂无对等连接。"
                        actionText="立即创建"
                        on-click="onCreate"
                    />
                    <!--<s-empty on-click="onCreate" class="{{iamPass.disable ? 'create-disable' : ''}}">
                </s-empty>-->
                </div>
                <div slot="c-peerConnId">
                    <span s-if="row.status === 'deleting'" class="truncated" title="{{row.peerConnId}}">
                        <s-tooltip content="{{row.peerConnId}}"> {{row.peerConnId}} </s-tooltip>
                    </span>
                    <span s-else>
                        <s-tooltip content="{{row.peerConnId}}">
                            <a
                                class="truncated"
                                title="{{row.peerConnId}}"
                                href="#/vpc/peerconn/detail?vpcId={{row.localVpcId}}&localIfId={{row.localIfId}}}"
                                data-testid="{{row.role === 'initiator' ? testID.peerconn.listItemInitiator: ''}}"
                            >
                                {{row.peerConnId}}</a
                            >
                        </s-tooltip>
                    </span>
                </div>
                <div slot="c-status">
                    <span class="{{row.status | statusClass}}">{{row.status | statusText}}</span>
                    <span s-if="{{row.status === 'consult_failed' || row.status === 'error'}}">
                        <s-popover placement="top">
                            <div slot="content">
                                <!--bca-disable-next-line-->
                                {{row.status | getTipText | raw}}
                            </div>
                            <s-icon class="tip-icon-wrap" name="warning-mark" />
                        </s-popover>
                    </span>
                </div>
                <div slot="h-localIf">
                    <span>本端接口名称/ID</span>
                    <s-popover placement="right">
                    <!--bca-disable-next-line-->
                        <div slot="content" s-html="routeConfigTipContent"></div>
                        <s-icon class="help-icon-wrap" name="question-mark" />
                    </s-popover>
                </div>
                <div slot="c-localIf">
                    <span class="truncated">{{row.localIfName}}</span>
                    <s-popover
                        s-ref="popover-localIfName-{{rowIndex}}"
                        placement="top"
                        trigger="click"
                        class="edit-popover-class"
                    >
                        <div class="edit-wrap" slot="content">
                            <s-input
                                value="{=edit.localIfName.value=}"
                                width="320"
                                placeholder="请输入名称"
                                on-input="onEditInput($event, rowIndex, 'localIfName')"
                            />
                            <div class="edit-tip">大小写字母、数字以及-_/.特殊字符，必须以字母开头，长度1-65</div>
                            <s-button
                                skin="primary"
                                s-ref="editBtn-localIfName-{{rowIndex}}"
                                disabled="{{true}}"
                                on-click="editConfirm(row, rowIndex, 'localIfName')"
                                >确定</s-button
                            >
                            <s-button on-click="editCancel(rowIndex, 'localIfName')">取消</s-button>
                        </div>
                        <outlined-editing-square
                            s-if="{{row.canEdit}}"
                            class="name-icon"
                            on-click="onInstantEdit(row, rowIndex, 'localIfName')"
                        />
                    </s-popover>
                    <br />
                    <span class="truncated">{{row.localIfId}}</span>
                    <s-clip-board class="name-icon" text="{{row.localIfId}}" />
                </div>
                <div slot="c-localVpcShortId">
                    <div class="truncated">
                        <s-tooltip content="{{row.localVpcName || '-'}}">
                            <a href="#/vpc/instance/detail?vpcId={{row.localVpcId}}">{{row.localVpcName || '-'}}</a>
                        </s-tooltip>
                    </div>

                    <span class="truncated">
                        <s-tooltip content="{{row.localVpcShortId || '-'}}"> {{row.localVpcShortId || '-'}} </s-tooltip>
                    </span>
                </div>
                <div slot="c-connType">{{row | getConnType}}</div>
                <div slot="c-peerRegion">{{row | getPeerRegion}}</div>
                <div slot="c-DNSSync">{{row | getDnsStatus}}</div>
                <div slot="c-peerVpcShortId">
                    <span class="truncated" title="{{row.peerCidr}}">{{row.peerCidr}}</span><br />
                    <span
                        class="truncated"
                        title="{{instance.peerAccountId === aihcAccountId ? '百舸AIHC' : row.peerVpcShortId}}"
                        >{{instance.peerAccountId === aihcAccountId ? '百舸AIHC' : row.peerVpcShortId}}</span
                    >
                </div>
                <div slot="c-bandwidth">{{row.bandwidth}}Mbps</div>
                <div slot="c-productType">
                    <span>{{row | getProductType}}</span>
                    <span
                        s-if="{{row.task === 'auto_renew' && row.productType === 'prepay'}}"
                        class="icon-auto-renew"
                        name="auto-renew"
                        title="已开通自动续费"
                    >
                    </span>
                    <span s-if="row.productType === 'prepay' && row.orderStatus === 'to_postpay'">
                        <s-popover placement="top">
                            <div slot="content">
                                {{'该实例已开通计费变更-预付费转后付费，将会在到期后转为后付费资源，请关注！如需进行续费、升级等操作，请先取消计费变更，谢谢！'}}
                            </div>
                            <s-icon class="tip-icon-wrap" name="warning-mark" />
                        </s-popover>
                    </span>
                </div>
                <div slot="c-role">
                    <span>{{row | getRoleText}}</span>
                    <span s-if="row.role === 'acceptor'">
                        <s-popover placement="top">
                            <div slot="content">接受端不支持操作，可在发起端进行操作</div>
                            <s-icon class="tip-icon-wrap" name="warning-mark" />
                        </s-popover>
                    </span>
                </div>
                <div slot="c-createTime">{{row.createTime | getTime}}</div>
                <div slot="c-expireTime">{{row.expireTime | getTime}}</div>
                <div slot="c-tag">
                    <span s-if="!row.tags || row.tags.length < 1"> - </span>
                    <div s-else s-for="item,index in row.tags">
                        <span s-if="index <= 1"> {{item.tagKey + ':' + item.tagValue}} </span>
                        <div s-if="row.tags.length > 2 && index === 1">...</div>
                    </div>
                </div>
                <div slot="c-resourceGroups">
                    <div s-if="row.resourceGroups && row.resourceGroups.length">
                        <p s-for="item in row.resourceGroups">{{item.name}}</p>
                    </div>
                    <span s-else>-</span>
                </div>
                <div slot="c-opt" class="operations">
                    <div class="operation-wrapper">
                        <s-tooltip content="{{row | accessChangeBw}}">
                            <s-button skin="stringfy" on-click="handleChangeBw(row)" disabled="{{row | accessChangeBw}}"
                                >带宽调整</s-button
                            >
                        </s-tooltip>
                        <s-button disabled="{{row.status !== 'active'}}" skin="stringfy" on-click="showMonitor(row)"
                            >监控</s-button
                        >
                    </div>
                    <div class="operation-wrapper">
                        <s-button
                            class="block_class"
                            disabled="{{row.status !== 'active'}}"
                            skin="stringfy"
                            on-click="alarmDetail(row)"
                            >报警详情</s-button
                        >
                        <s-button
                            disabled="{{row.status !== 'active'}}"
                            skin="stringfy"
                            on-click="changeResourceGroup(row)"
                            >编辑资源分组
                        </s-button>
                        <s-button
                            skin="stringfy"
                            s-if="isSupportAutoRenew(row) && !isShowCloseAutoRenew(row)"
                            on-click="handleAutoRenew(row, 'open')"
                            >设置自动续费</s-button
                        >
                        <s-button
                            skin="stringfy"
                            s-if="isSupportAutoRenew(row) && isShowCloseAutoRenew(row)"
                            on-click="handleAutoRenew(row, 'close')"
                            >关闭自动续费</s-button
                        >
                    </div>
                </div>
            </s-table>
            <s-pagination
                s-if="{{pager.total}}"
                slot="pager"
                layout="{{'total, pageSize, pager, go'}}"
                pageSize="{{pager.pageSize}}"
                total="{{pager.total}}"
                page="{{pager.page}}"
                on-pagerChange="onPagerChange"
                on-pagerSizeChange="onPagerSizeChange"
            />
            <resource-group-dialog
                s-if="{{showResource}}"
                sdk="{{resourceSDK}}"
                resource="{{resource}}"
                on-success="oncommit"
                on-cancel="onCancel"
            />
        </s-app-list-page>
    </div>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@invokeComp(
    '@peerconn-list-monitor',
    '@edit-tag',
    '@vpc-select',
    '@custom-column',
    '@search-tag',
    '@introduce-panel',
    '@table-empty'
)
class PeerConnList extends Component {
    static components = {
        'resource-group-dialog': ResourceGroupDialog,
        'outlined-refresh': OutlinedRefresh,
        'outlined-download': OutlinedDownload,
        'outlined-plus': OutlinedPlus,
        'outlined-editing-square': OutlinedEditingSquare,
        'outlined-link': OutlinedLink
    };
    static filters = {
        statusClass(value: string) {
            return PeerConnStatus.fromValue(value).styleClass || '';
        },
        statusText(value: string) {
            return value ? PeerConnStatus.getTextFromValue(value) : '-';
        },
        getProductType(item: any) {
            let str = PayType.getTextFromValue(item.productType);
            if (!str) {
                return '-';
            }

            if (item.productType === PayType.POSTPAY) {
                str +=
                    item.subProductType === 'bandwidth'
                        ? '-按带宽'
                        : item.subProductType === 'PeakBandwidth_Percent_95'
                          ? '-按传统型月95'
                          : '-按流量';
            }
            return str;
        },
        getTime(value: string) {
            return value ? utcToTime(value) : '-';
        },
        getTipText(status: string) {
            let ticketUrl = ContextService.Domains.ticket + '/#/ticket/create';
            if (status === PeerConnStatus.CONSULT_FAILED) {
                return '对方拒绝连接申请或已超时，该订单退款会退回您的帐户';
            }
            return `当前对等连接出现异常，请提交工单解决
                    <a href="${ticketUrl}" target="_BLANK">工单入口</a>`;
        },
        getConnType(item: any) {
            return item.peerAccountId === aihcAccountId ? '--' : PeerConnType.getTextFromValue(item.connType) || '-';
        },
        getPeerRegion(item: any) {
            return AllRegion.getTextFromValue(item.peerRegion);
        },
        getDnsStatus(item: any) {
            return DNSSyncStatus.getTextFromValue(item.dnsStatus);
        },
        getRoleText(item: any) {
            return PeerConnRole.getTextFromValue(item.role);
        },
        accessChangeBw(row: any) {
            const {
                UPGRADE: {message}
            } = checker.check(rules, [row], 'UPGRADE');
            return message;
        }
    };

    static computed = {
        operationDisabled() {
            const selectedItems = this.data.get('table.selectedItems');
            return selectedItems.length === 0;
        },
        routeConfigTipContent() {
            const docUrl = this.data.get('DocService.peer_index');
            const isXS = this.data.get('FLAG.NetworkSupportXS');
            if (!isXS) {
                return `在配置路由表下一跳时使用此ID，详情请查看<a href="${docUrl}" target="_BLANK">帮助文档</a>`;
            } else {
                return '在配置路由表下一跳时使用此ID，详情请查看帮助文档';
            }
        }
    };

    initData() {
        const allColumns = columns.slice();
        const customColumnDb = allColumns.map(item => ({
            text: item.label,
            value: item.name,
            disabled: item.name === 'peerConnId' || item.name === 'opt'
        }));
        return {
            FLAG,
            testID,
            DocService,
            isEnglish: isEnglishLocale(),
            searchbox: {
                keyword: '',
                placeholder: '请输入本端接口名称进行搜索',
                keywordType: ['localIfName'],
                keywordTypes: [
                    {value: 'localIfName', text: '本端接口名称'},
                    {value: 'localIfId', text: '本端接口ID'},
                    {value: 'peerConnId', text: '对等连接ID'},
                    {value: 'resGroupId', text: '资源组'},
                    {value: 'tag', text: '标签'}
                ]
            },
            OperationType: [
                {label: '带宽调整', value: 'UPGRADE'},
                {label: '释放', value: 'RELEASE'},
                {label: '计费变更', value: 'ALTER_PRODUCTTYPE'},
                {label: '取消计费变更', value: 'CANCEL_ALTER_PRODUCTTYPE'},
                {label: '开启DNS同步', value: 'OPEN_DNS_SYNC'},
                {label: '关闭DNS同步', value: 'CLOSE_DNS_SYNC'}
            ],
            customColumn: {
                value: ['peerConnId', 'status', 'localVpcShortId', 'peerVpcShortId', 'DNSSync', 'bandwidth', 'opt'],
                datasource: customColumnDb
            },
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: []
                },
                allColumns,
                datasource: [],
                selectedItems: []
            },
            pager: {
                pageSize: 10,
                page: 1,
                total: 0
            },
            order: {},
            showMode: '',
            applyListNum: 0,
            resourceSDK: new ResourceGroupSDK(window.$http, window.$context),
            iamPass: {},
            introduceTitle: '对等连接',
            description:
                '对等连接（Peer Connection）为用户提供了VPC级别的网络互联服务，使用户实现在不同虚拟网络之间的流量互通，实现同地域、跨地域，同账户、跨账户之间稳定高速的虚拟网络互联。',
            introduceIcon: INTRODUCE_ICON_COLOR,
            documentIcon: DOCUMENT_ICON,
            peerconnRelease: {},
            aihcAccountId,
            accountState: {
                disabled: false,
                message: ''
            },
            urlQuery: getQueryParams(),
            peerconnType: 'no_cross_border',
            inCrossWhite: false
        };
    }

    inited() {
        let vpcId = this.data.get('urlQuery.vpcId');
        const peerconnType = this.data.get('context.peerconnType');
        this.data.set('peerconnType', peerconnType);
        if (vpcId) {
            window.$storage.set('vpcId', vpcId);
        }
        this.getIamQuery();
        this.setOperationMessage();
        this.checkPeerCreate();
        this.handleJumpFromMirror();
        const accountState = window.$storage.get('accountState');
        this.data.set('accountState', accountState);
    }

    attached() {
        window.$storage.get('showPeerconnIntroduce') === false && this.data.set('show', false);
        this.data.set('introduceEle', this.ref('introduce'));
        this.watch('operation', value => {
            if (value !== '') {
                this.data.set('operation', '');
            }
        });
    }

    handleJumpFromMirror() {
        const id = this.data.get('urlQuery.id');
        if (id) {
            this.data.set('searchbox.keywordType', ['peerConnId']);
            this.data.set('searchbox.keyword', id);
        }
    }

    checkPeerCreate() {
        let {createPeer} = checker.check(rules, '', 'createPeer', '');
        this.data.set('createPeer', createPeer);
    }

    vpcInt() {
        this.loadPage();
        this.getWaitingList();
    }

    vpcChange() {
        this.data.set('pager.page', 1);
        this.loadPage();
        this.getWaitingList();
    }

    getPayload() {
        const searchParam = this.ref('search') ? this.ref('search').getSearchCriteria() : {};
        const {pager, order, filters, showMode, peerconnType} = this.data.get('');
        let payload = {
            type: peerconnType,
            pageNo: pager.page,
            pageSize: pager.pageSize,
            vpcId: window.$storage.get('vpcId'),
            localVpcShortId: (window.$storage.get('vpcInfo') && window.$storage.get('vpcInfo').shortId) || ''
        };
        if (!payload.vpcId) {
            delete payload.vpcId;
        }
        if (showMode) {
            payload.showMode = showMode;
        }

        return {...payload, ...order, ...filters, ...searchParam};
    }

    loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getPayload();
        this.resetTable();
        if (payload.keywordType === 'resGroupId') {
            payload.keywordType = 'resourceGroupName';
        }
        this.$http.getPeerList(payload).then(res => {
            let data = this.checkEdit(res.result);
            this.data.set('table.datasource', data);
            this.data.set('pager.total', res.totalCount);
            this.data.set('table.loading', false);
        });
    }

    checkEdit(data: any) {
        let editStatus = [PeerConnStatus.AUDIT_FAILED, PeerConnStatus.AUDITING, PeerConnStatus.AUDITOR_PAUSE];
        let result = data.map(item => {
            item.canEdit = !editStatus.includes(item);
            return {
                ...item
            };
        });
        return result;
    }

    resetTable() {
        this.data.set('table.selection', {
            mode: 'multi',
            selectedIndex: []
        });
        this.data.set('table.selectedItems', []);
    }

    refresh() {
        this.loadPage();
    }

    // 设置表格列
    setTableColumns(customColumnNames: any) {
        customColumnNames = customColumnNames ? customColumnNames : this.data.get('customColumn.value');
        const allColumns = this.data.get('table.allColumns');
        let columns = [];
        allColumns.forEach(item => customColumnNames.indexOf(item.name) > -1 && columns.push(item));
        this.data.set('table.columns', columns);
    }
    initColumns(value: any) {
        this.setTableColumns(value);
    }
    // 自定义表格列
    onCustomColumns(value: any) {
        this.data.set('customColumn.value', value);
        this.setTableColumns(value);
    }

    tableSelected(e: Event) {
        this.data.set('table.selectedItems', e.value.selectedItems);
        let {recharge} = checker.check(rules, e.value.selectedItems, '');
        this.data.set('recharge', recharge);
        this.setOperationMessage();
    }

    setOperationMessage() {
        let selectedItem = this.data.get('table.selectedItems');
        let checkResult = checker.check(rules, selectedItem, '');
        let OperationType = u.cloneDeep(this.data.get('OperationType'));
        const accountState = window.$storage.get('accountState');
        const {disabled, message} = accountState;
        OperationType.forEach(item => {
            if (checkResult[item.value]) {
                item.disabled = checkResult[item.value].disable;
                item.message = checkResult[item.value].message;
                if (item.value === 'RELEASE') {
                    this.data.set('peerconnRelease', {
                        disabled: checkResult[item.value].disable,
                        message: checkResult[item.value].message
                    });
                }
                // 计费变更 变配开启欠费校验
                if (['ALTER_PRODUCTTYPE', 'UPGRADE'].includes(item.value) && !item.disabled && disabled) {
                    item.disabled = true;
                    item.message = message;
                }
            }
        });
        this.data.set('OperationType', OperationType);
    }

    onOperationChange(e: Event) {
        const methodMap = {
            UPGRADE: this.upgradePeerconn,
            RELEASE: this.onRelease,
            ALTER_PRODUCTTYPE: this.alterProduct,
            CANCEL_ALTER_PRODUCTTYPE: this.cancelAlterProduct,
            OPEN_DNS_SYNC: this.openDns,
            CLOSE_DNS_SYNC: this.closeDns
        };
        let requester = methodMap[e.value].bind(this);
        const selectedItems = this.data.get('table.selectedItems');
        requester(selectedItems);
    }

    // 搜索事件
    onSearch() {
        this.data.set('pager.page', 1);
        this.loadPage();
    }
    // 改变页数
    onPagerChange(e: Event) {
        this.data.set('pager.page', e.value.page);
        this.loadPage();
    }

    // 改变每页显示数量
    onPagerSizeChange(e: Event) {
        this.data.set('pager.page', 1);
        this.data.set('pager.pageSize', e.value.pageSize);
        this.loadPage();
    }

    // 排序
    onSort(e: Event) {
        this.data.set('order', e.value);
        this.loadPage();
    }

    // 筛选
    onFilter(e: Event) {
        let name = e.field.name;
        let value = e[name] ? e[name] : '';
        this.data.set('filters.' + name, value);
        this.data.set('pager.page', 1);
        this.loadPage();
    }

    showExpireData() {
        let showMode = this.data.get('showMode');
        showMode = showMode ? '' : 'WILLEXPIRED';
        this.data.set('showMode', showMode);
        this.loadPage();
    }

    getWaitingList() {
        const vpcInfo = window.$storage.get('vpcInfo');
        const payload = {
            localVpcShortId: vpcInfo.shortId || '',
            pageSize: 10000,
            pageNo: 1
        };
        this.$http.getPeerWaitList(payload).then(data => {
            let {applyPeer} = checker.check(rules, '', 'applyPeer', {data});
            this.data.set('applyPeer', applyPeer);
            this.data.set('applyListNum', data.result.length || 0);
        });
    }

    applyPeer() {
        const vpcInfo = window.$storage.get('vpcInfo');
        const dialog = new WaitingDialog({
            data: {
                vpcInfo
            }
        });
        dialog.attach(document.body);
        dialog.on('finsh', () => {
            this.loadPage();
            this.getWaitingList();
        });
    }

    downLoadList() {
        const {searchbox} = this.data.get('');
        const vpcId = window.$storage.get('vpcId');
        const {keywordType, keyword} = searchbox;
        let payload = {
            vpcId,
            keyword
        };
        if (keywordType.length > 1) {
            payload.keywordType = keywordType[0];
        }
        payload = this.filterNullValue(payload);
        let url = urlSerialize(payload);
        window.open(`/api/peerconn/peerconn/downloads?${url}`);
    }

    filterNullValue(obj: any) {
        let myObj = u.cloneDeep(obj);
        for (let i in myObj) {
            if (myObj.hasOwnProperty(i) && myObj[i] === '') {
                delete myObj[i];
            }
        }
        return myObj;
    }

    // 点击修改icon
    onInstantEdit(row: any, rowIndex: number, type: string) {
        this.data.set(`edit.${type}.value`, row[`${type}`]);
        this.data.set(`edit.${type}.error`, false);
        const popWrap = this.ref(`popover-${type}-${rowIndex}`);
        popWrap.data.set('visible', !popWrap.data.get('visible'));
    }
    // 编辑弹框-输入名称/描述
    onEditInput(e: Event, rowIndex: number, type: string) {
        let result =
            type === 'localIfName'
                ? e.value === '' || !/^[a-zA-Z][\w\-\_\/\.]{0,64}$/.test(e.value)
                : e.value.length > 200;
        this.data.set(`edit.${type}.error`, result);
        this.data.set(`edit.${type}.value`, e.value);
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', result);
    }

    // 编辑弹框-提交
    editConfirm(row: any, rowIndex: number, type: string) {
        let edit = this.data.get(`edit.${type}`);
        if (edit.error) {
            return;
        }
        this.$http
            .peerUpdate({
                localIfId: row.localIfId,
                [type]: edit.value,
                peerConnId: row.peerConnId
            })
            .then(() => {
                this.editCancel(rowIndex, type);
                Notification.success('修改成功');
                this.loadPage();
            });
    }

    // 编辑弹框-取消
    editCancel(rowIndex: number, type: string) {
        this.ref(`editBtn-${type}-${rowIndex}`).data.set('disabled', true);
        this.ref(`popover-${type}-${rowIndex}`).data.set('visible', false);
    }

    upgradePeerconn(selectedItems: any) {
        const current = selectedItems[0];
        location.hash = `#/vpc/peerconn/upgrade?vpcId=${current.localVpcId}&localIfId=${current.localIfId}`;
    }

    onCreate() {
        let vpcId = window.$storage.get('vpcId');
        console.log(vpcId, 'vpcId');
        const peerconnType = this.data.get('peerconnType');
        const type = peerconnType === 'cross_border' ? 'cross' : 'normal';
        location.hash = `#/vpc/peerconn/create/v2?vpcId=${vpcId}&type=${type}`;
    }

    onRecharge() {
        const selectedItems = this.data.get('table.selectedItems');
        let isCross = u.every(selectedItems, item => {
            return isCrossRegion(item.localRegion, item.peerRegion);
        });
        let ids = u.pluck(selectedItems, 'peerConnId');
        let api = isCross
            ? '/api/peerconn/crossBorder/order/confirm/renew'
            : '/api/peerconn/peerconn/order/confirm/renew';
        if (isCross) {
            return (location.hash = `#/vpc/peerconn/recharge?vpcId=
                ${selectedItems[0].localVpcId}}&localIfId=${selectedItems[0].localIfId}`);
        }
        let serviceType = isCross ? 'MKT' : 'PEERCONN';
        recharge(serviceType, ids, null, api);
    }

    onRelease(selectedItems: any) {
        const peerConnId = selectedItems[0].peerConnId;
        const confirm = new Confirm({
            data: {
                title: '删除提示',
                content: '对等连接删除后不能恢复，您确定删除吗？'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.deletePeer({peerConnId}).then(() => {
                Notification.success('释放成功');
                this.loadPage();
            });
        });
    }

    alterProduct(selectedItems: any) {
        let dialog = new alertDialog({
            data: {
                title: '计费变更',
                item: selectedItems[0]
            }
        });
        dialog.attach(document.body);
    }

    cancelAlterProduct(selectedItems: any) {
        let instanceIds = u.pluck(selectedItems, 'peerConnId');
        let localVpcShortId = selectedItems[0].localVpcShortId;

        let confirm = new Confirm({
            data: {
                title: '取消计费变更',
                content:
                    '确认取消计费变更？<br>您已开通计费变更-预付费转后付费功能。' +
                    '<br>实例将会在到期后自动转换为后付费的计费方式。'
            }
        });
        confirm.attach(document.body);
        confirm.on('confirm', () => {
            this.$http.peerCancelAlterProductType({instanceIds, localVpcShortId}).then(() => {
                Notification.success('取消计费变更成功');
                this.loadPage();
            });
        });
    }

    openDns(selectedItems: any) {
        const {peerConnId, role} = selectedItems[0];
        this.$http.changeDnsStatus({peerConnId, role, action: 'open_dns'}).then(() => {
            Notification.success('开启DNS同步成功！');
            this.loadPage();
        });
    }

    closeDns(selectedItems: any) {
        const {peerConnId, role} = selectedItems[0];
        this.$http.changeDnsStatus({peerConnId, role, action: 'close_dns'}).then(() => {
            Notification.success('关闭DNS同步成功！');
            this.loadPage();
        });
    }

    alarmDetail(row: any) {
        redirect('/bcm/#/bcm/alarm/rule/list~scope=BCE_PEERCON');
    }

    showMonitor(row: any) {
        const {vpcId} = window.$storage.get('vpcId');
        const dialog = new Moinitor({
            data: {
                vpcId,
                localIfId: row.localIfId
            }
        });
        dialog.attach(document.body);
    }

    // 编辑资源分组确定后
    oncommit() {
        this.data.set('showResource', false);
        this.loadPage();
    }
    // 编辑资源分组取消后
    onCancel() {
        this.data.set('showResource', false);
    }
    // 编辑资源分组
    changeResourceGroup(row: any) {
        let resource = {
            resourceId: row.localIfId,
            serviceType: 'PEERCONN'
        };
        this.data.set('resource', resource);
        this.data.set('showResource', true);
    }

    onRegionChange() {
        location.reload();
    }
    getIamQuery() {
        this.$http.getInterfaceIam({interfaceName: 'createPeerConn'}).then((res: any) => {
            let message = '';
            !res.interfacePermission && (message += '创建对等连接权限');
            !res.createOrderPermission && (message += message ? '、创建订单权限' : '创建订单权限');
            !res.payOrderPermission && (message += message ? '、支付订单权限' : '');
            if (!res.requestId && !res.masterAccount) {
                if (message) {
                    this.data.set('iamPass', {
                        disable: true,
                        message: `您没有${message}，请联系主用户添加`
                    });
                } else {
                    this.data.set('iamPass', {
                        disable: false,
                        message: ''
                    });
                }
            } else {
                this.data.set('iamPass', {
                    disable: false,
                    message: ''
                });
            }
        });
    }
    /**
     * @description 介绍卡片变化时回调
     * @param {boolean} flag
     */
    handleToggle(flag: boolean) {
        this.data.set('show', flag);
        window.$storage.set('showPeerconnIntroduce', false);
        this.data.set('introduceIcon', INTRODUCE_ICON);
        const ele = this.data.get('introduceEle');
        ele.style.color = '#151B26';
    }
    handleShowCard() {
        if (!this.data.get('show')) {
            this.data.set('show', true);
            window.$storage.set('showPeerconnIntroduce', true);
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
        }
    }
    handleMouseEnter(type: string) {
        if (type === 'introduce') {
            this.data.set('introduceIcon', INTRODUCE_ICON_COLOR);
            const ele = this.data.get('introduceEle');
            ele.style.color = '#2468F2';
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON_COLOR);
        }
    }
    handleMouseLeave(type: string) {
        const isShow = this.data.get('show');
        if (type === 'introduce') {
            this.data.set('introduceIcon', isShow ? INTRODUCE_ICON_COLOR : INTRODUCE_ICON);
            const ele = this.data.get('introduceEle');
            !isShow && (ele.style.color = '#151B26');
        } else {
            this.data.set('documentIcon', DOCUMENT_ICON);
        }
    }
    handleChangeBw(row: Record<string, any>) {
        const {localVpcId, localIfId} = row;
        location.hash = `#/vpc/peerconn/upgrade?vpcId=${localVpcId}&localIfId=${localIfId}`;
    }
    isSupportAutoRenew(row: any) {
        const {localRegion, peerRegion, status} = row;
        return isCrossRegion(localRegion, peerRegion) && [PeerConnStatus.ACTIVE].includes(status);
    }
    isShowCloseAutoRenew(row: any) {
        const {task} = row;
        return task === 'auto_renew';
    }
    handleAutoRenew(row: any, type: 'open' | 'close') {
        if (type === 'open') {
            const instance = new CrossAutoRenewDialog({
                data: {
                    title: '设置自动续费',
                    rowData: row
                }
            });
            instance.on('renewSuccess', () => {
                Notification.success(`实例${row.peerConnId}已成功设置自动续费`);
                this.loadPage();
            });
            instance.attach(document.body);
        } else {
            const confirmTip = {
                title: '温馨提示',
                content: '请确认是否对当前实例关闭自动续费？'
            };
            const confirmFn = async () => {
                try {
                    const {peerConnId} = row;
                    const payload = {
                        instanceId: peerConnId
                    };
                    const res = await this.$http.closeCrossRegionAutoRenew(payload);
                    Notification.success(`实例${peerConnId}已成功关闭自动续费`);
                    this.loadPage();
                } catch (error) {}
            };
            confirmValidate(confirmTip, confirmFn);
        }
    }
}
export default San2React(Processor.autowireUnCheckCmpt(PeerConnList));
